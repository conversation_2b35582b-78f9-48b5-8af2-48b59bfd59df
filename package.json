{"name": "PlateMate", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5", "@kilohealth/rn-fitness-tracker": "^3.1.0", "@react-native-async-storage/async-storage": "^1.22.3", "@react-native-clipboard/clipboard": "^1.13.2", "@react-native-community/datetimepicker": "^8.1.1", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-navigation/bottom-tabs": "^6.5.12", "@react-navigation/native": "^6.1.10", "@react-navigation/native-stack": "^6.9.18", "@reduxjs/toolkit": "^2.2.1", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@shopify/flash-list": "^1.8.2", "@twotalltotems/react-native-otp-input": "^1.3.11", "@types/redux-logger": "^3.0.13", "axios": "^1.6.8", "formik": "^2.4.5", "i18next": "^23.11.5", "lottie-ios": "4.4.1", "lottie-react-native": "^6.7.0", "moment": "^2.30.1", "react": "18.2.0", "react-i18next": "^14.1.2", "react-native": "0.73.4", "react-native-calendar-strip": "^2.2.6", "react-native-camera": "^4.2.1", "react-native-circular-progress-indicator": "^4.4.2", "react-native-device-info": "^11.1.0", "react-native-document-picker": "^9.1.2", "react-native-emoji-selector": "^0.2.0", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.20.0", "react-native-gifted-charts": "^1.4.7", "react-native-gifted-chat": "2.0.1", "react-native-google-fit": "^0.21.0", "react-native-image-crop-picker": "^0.41.3", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-modal-datetime-picker": "^17.1.0", "react-native-navigation-bar-color": "^2.0.2", "react-native-permissions": "^4.1.5", "react-native-popup-menu": "^0.16.1", "react-native-progress": "^5.0.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.1", "react-native-reanimated": "3.7.1", "react-native-ruler-picker": "^0.2.2", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "3.29.0", "react-native-shake": "^5.6.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.1.0", "react-native-svg-transformer": "^1.3.0", "react-native-system-navigation-bar": "^2.6.4", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3", "react-native-wheel-scrollview-picker": "^2.0.9", "react-redux": "^9.1.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "rn-fetch-blob": "^0.12.0", "socket.io-client": "^4.7.5", "use-debounce": "^10.0.0", "yup": "^1.3.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}