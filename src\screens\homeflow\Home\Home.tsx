import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useEffect, useState} from 'react';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {Header} from '../../../components';
import {ScrollView} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomStackParamList} from '../../../navigation/BottomNavigation/BottomNavigation';
import SystemNavigationBar from 'react-native-system-navigation-bar';
import MealPost from './MealPost';
import images from '../../../assets/images';
import usePosts from '../../../hooks/models/usePosts';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<BottomStackParamList, 'Home'>;

const Home: React.FC<Props> = ({navigation}) => {
  const {posts, isLoading} = usePosts();
  const [refresh, setRefresh] = useState(0);
  const handleRefresh = () => setRefresh(n => n + 1);
  const {t} = useTranslation();

  useEffect(() => {
    SystemNavigationBar.setNavigationColor(theme.lightColors?.primary || '');
  }, []);

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        {/* HEADER */}
        <Header
          onBellPress={() => navigation.navigate('Notifications')}
          onAvatarPress={() => navigation.navigate('Profile')}
        />
        {/* CLIENTS */}
        {posts?.length ? (
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{marginTop: 16}}
            contentContainerStyle={{paddingBottom: 48}}>
            {posts.map(item => {
              return (
                <MealPost
                  key={item._id + item.createdAt}
                  mealPost={item}
                  onAddReviewSuccess={handleRefresh}
                />
              );
            })}
          </ScrollView>
        ) : null}

        {/* EMPTY STATE */}
        {isLoading ? (
          <ActivityIndicator
            color={theme.lightColors?.primary}
            size={'large'}
            style={{alignSelf: 'center', marginTop: 100}}
          />
        ) : !posts.length ? (
          <View style={{alignItems: 'center', marginTop: 80}}>
            <Image
              source={images.noPost}
              style={styles.noChatImage}
              resizeMode="contain"
            />
            <Text style={styles.noPostHeading}>
              {t('No meal added by client.')}
            </Text>
          </View>
        ) : null}
      </View>
    </SafeAreaView>
  );
};

export default Home;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  clientCard: {
    marginTop: 20,
  },
  emptyState: {
    color: theme.lightColors?.black,
    textAlign: 'center',
    marginVertical: 24,
  },
  noChatImage: {
    height: 120,
    width: 150,
    opacity: 0.7,
  },
  noPostHeading: {
    color: '#00000080',
    marginTop: 16,
    fontWeight: '600',
    fontSize: 16,
    fontFamily: Fonts.bold,
  },
  notPostText: {
    fontSize: 12,
    color: '#00000080',
    marginTop: 8,
  },
});
