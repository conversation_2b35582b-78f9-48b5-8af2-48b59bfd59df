import React, {useEffect, useRef} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import HomeStackNavigator from './HomeStack/HomeStackNavigator';
import {useAppDispatch, useAppSelector} from '../store';
import AuthStackNavigator from './AuthNavigation/AuthNavigation';
import ClientStackNavigator from './ClientStackNavigator/ClientStackNavigator';
import Toast from 'react-native-toast-message';
import {loadLanguage} from '../store/LanguagesSlice';
import useFetchers from '../hooks/exec/useFetchers';
import {setStepCount} from '../store/userSlice';
import {DeeplinkHandlerHOC} from '../services/firebaseMessaging';
// import {
//   AuthorizationPermissions,
//   FitnessDataType,
//   FitnessTracker,
//   GoogleFitDataType,
//   HealthKitDataType,
//   HealthKit,
//   HealthKitWriteData,
//   HealthKitUnitType,
// } from '@kilohealth/rn-fitness-tracker';
import GoogleFit, {Scopes} from 'react-native-google-fit';
import {initializeSocket, disconnectSocket} from '../services/socketService';
import NavigationService from './NavigationService';

// import {startCounter, stopCounter} from 'react-native-accurate-step-counter';

const RootNavigation = () => {
  const user = useAppSelector(state => state.user.user);
  const dispatch = useAppDispatch();
  useFetchers();

  useEffect(() => {
    if (user._id) {
      initializeSocket(user._id);
    }

    return () => {
      disconnectSocket(); // Cleanup socket on component unmount
    };
  }, [user._id]);

  useEffect(() => {
    dispatch(loadLanguage());
    // getStepsToday();
    // checkStep();
  }, []);
  // useEffect(() => {
  //   const config = {
  //     default_threshold: 15.0,
  //     default_delay: 150000000,
  //     cheatInterval: 3000,
  //     onStepCountChange: (stepCount: number) => {
  //       dispatch(setStepCount(stepCount));
  //     },
  //     onCheat: () => {
  //       console.log('User is Cheating');
  //     },
  //   };
  //   startCounter(config);
  //   return () => {
  //     stopCounter();
  //   };
  // }, []);
  // const permissions: AuthorizationPermissions = {
  //   healthReadPermissions: [HealthKitDataType.StepCount],
  //   googleFitReadPermissions: [GoogleFitDataType.Steps],
  // };

  // const getStepsToday = async () => {
  //   try {
  //     const authorized = await FitnessTracker.authorize(permissions);

  //     if (!authorized) return;

  //     const stepsToday = await FitnessTracker.getStatisticTodayTotal(
  //       FitnessDataType.Steps,
  //     );

  //     // returns the number of steps walked today, e.g. 320
  //     console.log(stepsToday);
  //     dispatch(setStepCount(stepsToday));
  //   } catch (error) {
  //     // Handle error here
  //     console.log(error);
  //   }
  // };

  const checkStep = async () => {
    await GoogleFit.checkIsAuthorized();
    console.log(GoogleFit.isAuthorized);

    const options = {
      scopes: [
        Scopes.FITNESS_ACTIVITY_READ,
        Scopes.FITNESS_ACTIVITY_WRITE,
        Scopes.FITNESS_BODY_READ,
        Scopes.FITNESS_BODY_WRITE,
      ],
    };
    GoogleFit.authorize(options)
      .then(authResult => {
        if (authResult.success) {
          console.log('AUTH_SUCCESS');
        } else {
          console.log('AUTH_DENIED', authResult.message);
        }
      })
      .catch(() => {
        console.log('AUTH_ERROR');
      });

    // ...
    const res = await GoogleFit.getDailyStepCountSamples(opt);
    console.log(res);
    // Call when authorized
    GoogleFit.startRecording(callback => {
      // Process data from Google Fit Recording API (no google fit app needed)
      console.log(callback);
    });
  };

  return (
    <NavigationContainer
      ref={ref => NavigationService.setTopLevelNavigator(ref)}>
      {!user._id ? <AuthStackNavigator /> : null}
      {user._id && user.userType === 'client' ? <ClientStackNavigator /> : null}
      {user._id && user.userType === 'coach' ? <HomeStackNavigator /> : null}
      <Toast position="top" />
    </NavigationContainer>
  );
};

export default DeeplinkHandlerHOC(RootNavigation);
