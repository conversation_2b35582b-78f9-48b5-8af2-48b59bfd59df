import messaging from '@react-native-firebase/messaging';
import {useEffect} from 'react';
// import { setFcmToken } from "../redux/slices/fcmTokenSlice";
import {PermissionsAndroid, Platform} from 'react-native';
import Toast from 'react-native-toast-message';
import {useAppDispatch, useAppSelector} from '../store';
import NavigationService from '../navigation/NavigationService';

export const DeeplinkHandlerHOC = RootNavigator => {
  const handleNotificationFlow = remoteMessage => {
    console.log(
      '->handle Notification Flow ---> ',
      remoteMessage,
      '||',
      remoteMessage.data,
    );

    const parseData = remoteMessage.data;
    const screen = parseData?.screen;
    const chatId = parseData?.chatId;
    const receiverId = parseData?.receiverId;

    if (screen === 'ChatDetails') {
      NavigationService.navigate(screen, {
        chatId: chatId,
        recipient: {
          _id: receiverId,
        },
      });
    } else {
      console.log(' Screen not found');
    }
  };

  const MessageHandlerComponent = () => {
    // const dispatch = useAppDispatch();
    const user = useAppSelector(state => state.user.user);

    const getFCMToken = async () => {
      try {
        await messaging().registerDeviceForRemoteMessages();
        const fcmToken = await messaging().getToken();
        if (fcmToken) {
          // setFcmToken(fcmToken);
          console.log('FCM Token:', fcmToken);
        }
      } catch (error) {
        console.log('Error in fetching FCM token:', error);
      }
    };

    const notificationListener = async () => {
      messaging().onNotificationOpenedApp(remoteMessage => {
        handleNotificationFlow(remoteMessage);
      });

      messaging()
        .getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log(
              'Notification caused app to open from quit state:',
              remoteMessage,
            );
          }
        });

      messaging().onMessage(async remoteMessage => {
        console.log('Notification received in foreground:', remoteMessage);
        // Show in-app notification or handle as needed
        if (remoteMessage.notification) {
          Toast.show({
            type: 'info',
            text1: remoteMessage.notification.title || 'New Message',
            text2: remoteMessage.notification.body,
            onPress: () => handleNotificationFlow(remoteMessage),
          });
        }
      });
    };

    const requestAndroidNotificationPermission = async () => {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Notification permission granted.');
          return true;
        } else {
          console.log('Notification permission denied.');
          return false;
        }
      }
      return true;
    };

    const requestUserPermission = async () => {
      if (Platform.OS === 'android') {
        const hasPermission = await requestAndroidNotificationPermission();
        if (!hasPermission) return;
      }

      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('FCM permission granted');
      } else {
        console.log('FCM permission denied');
      }
    };

    useEffect(() => {
      requestUserPermission();
      getFCMToken();
      notificationListener();
    }, []);

    return <RootNavigator />;
  };
  return MessageHandlerComponent;
};
